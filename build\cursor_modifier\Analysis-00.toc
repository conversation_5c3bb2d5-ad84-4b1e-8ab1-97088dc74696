(['D:\\py_projects\\cursor\\cursor_modifier.py'],
 ['D:\\py_projects\\cursor'],
 [],
 [('D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.12.8 (tags/v3.12.8:2dc476b, Dec  3 2024, 19:30:04) [MSC v.1942 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('cursor_modifier',
   'D:\\py_projects\\cursor\\cursor_modifier.py',
   'PYSOURCE')],
 [('zipfile', 'D:\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('argparse', 'D:\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('py_compile', 'D:\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'D:\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc', 'D:\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'D:\\Python312\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'D:\\Python312\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\Python312\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\Python312\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'D:\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\Python312\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'D:\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('socket', 'D:\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'D:\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('email', 'D:\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Python312\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('csv', 'D:\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('lzma', 'D:\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression', 'D:\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('bz2', 'D:\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('threading', 'D:\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Python312\\Lib\\_threading_local.py', 'PYMODULE'),
  ('struct', 'D:\\Python312\\Lib\\struct.py', 'PYMODULE'),
  ('importlib.util', 'D:\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('stringprep', 'D:\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('pickle', 'D:\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('ctypes', 'D:\\Python312\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\Python312\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('typing', 'D:\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('datetime', 'D:\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_strptime', 'D:\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('pathlib', 'D:\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('platform', 'D:\\Python312\\Lib\\platform.py', 'PYMODULE'),
  ('tempfile', 'D:\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('subprocess', 'D:\\Python312\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('hashlib', 'D:\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('string', 'D:\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('random', 'D:\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('bisect', 'D:\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('psutil',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('shutil', 'D:\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('uuid', 'D:\\Python312\\Lib\\uuid.py', 'PYMODULE'),
  ('json', 'D:\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Python312\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Python312\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Python312\\Lib\\json\\scanner.py', 'PYMODULE')],
 [('python312.dll', 'D:\\Python312\\python312.dll', 'BINARY'),
  ('unicodedata.pyd', 'D:\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Python312\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\Python312\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\Python312\\VCRUNTIME140.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\Python312\\DLLs\\libffi-8.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\Python312\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\Python312\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('python3.dll', 'D:\\Python312\\python3.dll', 'BINARY')],
 [],
 [],
 [('base_library.zip',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\base_library.zip',
   'DATA')],
 [('weakref', 'D:\\Python312\\Lib\\weakref.py', 'PYMODULE'),
  ('ntpath', 'D:\\Python312\\Lib\\ntpath.py', 'PYMODULE'),
  ('sre_constants', 'D:\\Python312\\Lib\\sre_constants.py', 'PYMODULE'),
  ('types', 'D:\\Python312\\Lib\\types.py', 'PYMODULE'),
  ('reprlib', 'D:\\Python312\\Lib\\reprlib.py', 'PYMODULE'),
  ('copyreg', 'D:\\Python312\\Lib\\copyreg.py', 'PYMODULE'),
  ('io', 'D:\\Python312\\Lib\\io.py', 'PYMODULE'),
  ('functools', 'D:\\Python312\\Lib\\functools.py', 'PYMODULE'),
  ('posixpath', 'D:\\Python312\\Lib\\posixpath.py', 'PYMODULE'),
  ('_collections_abc', 'D:\\Python312\\Lib\\_collections_abc.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Python312\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\Python312\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Python312\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'D:\\Python312\\Lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'D:\\Python312\\Lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Python312\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Python312\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'D:\\Python312\\Lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Python312\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Python312\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'D:\\Python312\\Lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Python312\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Python312\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\Python312\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Python312\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Python312\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Python312\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'D:\\Python312\\Lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Python312\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Python312\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\Python312\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\Python312\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos', 'D:\\Python312\\Lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'D:\\Python312\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'D:\\Python312\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Python312\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Python312\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Python312\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Python312\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Python312\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Python312\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Python312\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Python312\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Python312\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Python312\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\Python312\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048', 'D:\\Python312\\Lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'D:\\Python312\\Lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'D:\\Python312\\Lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'D:\\Python312\\Lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'D:\\Python312\\Lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Python312\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Python312\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Python312\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Python312\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Python312\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Python312\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Python312\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Python312\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Python312\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Python312\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Python312\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Python312\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Python312\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Python312\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Python312\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Python312\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Python312\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Python312\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Python312\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Python312\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Python312\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Python312\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'D:\\Python312\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'D:\\Python312\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Python312\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Python312\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'D:\\Python312\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'D:\\Python312\\Lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030',
   'D:\\Python312\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr', 'D:\\Python312\\Lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'D:\\Python312\\Lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Python312\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Python312\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'D:\\Python312\\Lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'D:\\Python312\\Lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'D:\\Python312\\Lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'D:\\Python312\\Lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'D:\\Python312\\Lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'D:\\Python312\\Lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'D:\\Python312\\Lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'D:\\Python312\\Lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'D:\\Python312\\Lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'D:\\Python312\\Lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'D:\\Python312\\Lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'D:\\Python312\\Lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'D:\\Python312\\Lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'D:\\Python312\\Lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'D:\\Python312\\Lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'D:\\Python312\\Lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'D:\\Python312\\Lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'D:\\Python312\\Lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'D:\\Python312\\Lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'D:\\Python312\\Lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'D:\\Python312\\Lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'D:\\Python312\\Lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'D:\\Python312\\Lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'D:\\Python312\\Lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'D:\\Python312\\Lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'D:\\Python312\\Lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'D:\\Python312\\Lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'D:\\Python312\\Lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'D:\\Python312\\Lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'D:\\Python312\\Lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'D:\\Python312\\Lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'D:\\Python312\\Lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'D:\\Python312\\Lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'D:\\Python312\\Lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'D:\\Python312\\Lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'D:\\Python312\\Lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'D:\\Python312\\Lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'D:\\Python312\\Lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'D:\\Python312\\Lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'D:\\Python312\\Lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap',
   'D:\\Python312\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Python312\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Python312\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'D:\\Python312\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Python312\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'D:\\Python312\\Lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases',
   'D:\\Python312\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'D:\\Python312\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('abc', 'D:\\Python312\\Lib\\abc.py', 'PYMODULE'),
  ('enum', 'D:\\Python312\\Lib\\enum.py', 'PYMODULE'),
  ('traceback', 'D:\\Python312\\Lib\\traceback.py', 'PYMODULE'),
  ('codecs', 'D:\\Python312\\Lib\\codecs.py', 'PYMODULE'),
  ('sre_parse', 'D:\\Python312\\Lib\\sre_parse.py', 'PYMODULE'),
  ('heapq', 'D:\\Python312\\Lib\\heapq.py', 'PYMODULE'),
  ('warnings', 'D:\\Python312\\Lib\\warnings.py', 'PYMODULE'),
  ('genericpath', 'D:\\Python312\\Lib\\genericpath.py', 'PYMODULE'),
  ('keyword', 'D:\\Python312\\Lib\\keyword.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\Python312\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('collections.abc', 'D:\\Python312\\Lib\\collections\\abc.py', 'PYMODULE'),
  ('collections', 'D:\\Python312\\Lib\\collections\\__init__.py', 'PYMODULE'),
  ('linecache', 'D:\\Python312\\Lib\\linecache.py', 'PYMODULE'),
  ('locale', 'D:\\Python312\\Lib\\locale.py', 'PYMODULE'),
  ('operator', 'D:\\Python312\\Lib\\operator.py', 'PYMODULE'),
  ('re._parser', 'D:\\Python312\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'D:\\Python312\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'D:\\Python312\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'D:\\Python312\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('sre_compile', 'D:\\Python312\\Lib\\sre_compile.py', 'PYMODULE'),
  ('stat', 'D:\\Python312\\Lib\\stat.py', 'PYMODULE'),
  ('re', 'D:\\Python312\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('os', 'D:\\Python312\\Lib\\os.py', 'PYMODULE')])
