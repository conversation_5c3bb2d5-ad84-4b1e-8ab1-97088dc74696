('D:\\py_projects\\cursor\\dist\\cursor_modifier.exe',
 True,
 False,
 False,
 'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 <PERSON>als<PERSON>,
 <PERSON>als<PERSON>,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'D:\\py_projects\\cursor\\build\\cursor_modifier\\cursor_modifier.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('cursor_modifier',
   'D:\\py_projects\\cursor\\cursor_modifier.py',
   'PYSOURCE'),
  ('python312.dll', 'D:\\Python312\\python312.dll', 'BINARY'),
  ('unicodedata.pyd', 'D:\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Python312\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\Python312\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\Python312\\VCRUNTIME140.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\Python312\\DLLs\\libffi-8.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\Python312\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\Python312\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('python3.dll', 'D:\\Python312\\python3.dll', 'BINARY'),
  ('base_library.zip',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\base_library.zip',
   'DATA')],
 [],
 False,
 True,
 1752946626,
 [('run.exe',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'D:\\Python312\\python312.dll')
