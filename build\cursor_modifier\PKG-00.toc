('D:\\py_projects\\cursor\\build\\cursor_modifier\\cursor_modifier.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('cursor_modifier',
   'D:\\py_projects\\cursor\\cursor_modifier.py',
   'PYSOURCE'),
  ('python312.dll', 'D:\\Python312\\python312.dll', 'BINARY'),
  ('unicodedata.pyd', 'D:\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Python312\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\Python312\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\Python312\\VCRUNTIME140.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\Python312\\DLLs\\libffi-8.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\Python312\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\Python312\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('python3.dll', 'D:\\Python312\\python3.dll', 'BINARY'),
  ('base_library.zip',
   'D:\\py_projects\\cursor\\build\\cursor_modifier\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 True,
 [],
 None,
 None,
 None)
