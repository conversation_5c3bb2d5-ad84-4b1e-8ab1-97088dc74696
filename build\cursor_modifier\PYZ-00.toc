('D:\\py_projects\\cursor\\build\\cursor_modifier\\PYZ-00.pyz',
 [('_compat_pickle', 'D:\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Python312\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'D:\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\Python312\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\Python312\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\Python312\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'D:\\Python312\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Python312\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\Python312\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\Python312\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\Python312\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'D:\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib', 'D:\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Python312\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Python312\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Python312\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'D:\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('platform', 'D:\\Python312\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\py_projects\\cursor\\venv\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'D:\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'D:\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'D:\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('statistics', 'D:\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Python312\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('uuid', 'D:\\Python312\\Lib\\uuid.py', 'PYMODULE'),
  ('zipfile', 'D:\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
